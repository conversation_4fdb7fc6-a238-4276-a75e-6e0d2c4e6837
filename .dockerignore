test.py
test

# Git
.git
.gitignore
.github

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
.env
.venv

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Docker
Dockerfile
docker-compose.yml
.docker/

# Logs
*.log
logs/

# Test
test/
tests/
.coverage
.pytest_cache/
htmlcov/

# Documentation
docs/
*.md
