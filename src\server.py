import os
import json
import urllib.request
import urllib.parse
from typing import Any
from client.comfyui import ComfyUI
from mcp.server.fastmcp import FastMCP
from dotenv import load_dotenv

load_dotenv()

mcp = FastMCP("comfyui")

@mcp.tool()
async def text_to_image(prompt: str, seed: int, steps: int, cfg: float, denoise: float, return_prompt_id: bool = False) -> Any:
    """Generate an image from a prompt.

    Args:
        prompt: The prompt to generate the image from.
        seed: The seed to use for the image generation.
        steps: The number of steps to use for the image generation.
        cfg: The CFG scale to use for the image generation.
        denoise: The denoise strength to use for the image generation.
        return_prompt_id: Whether to return the prompt ID along with images for status tracking.
    """
    auth = os.environ.get("COMFYUI_AUTHENTICATION")
    comfy = ComfyUI(
        url=f'http://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}',
        authentication=auth
    )
    result = await comfy.process_workflow(
        "text_to_image",
        {"prompt": prompt, "seed": seed, "steps": steps, "cfg": cfg, "denoise": denoise},
        return_url=os.environ.get("RETURN_URL", "true").lower() == "true",
        return_prompt_id=return_prompt_id
    )
    return result

@mcp.tool()
async def download_image(url: str, save_path: str) -> Any:
    """Download an image from a URL and save it to a file.
    
    Args:
        url: The URL of the image to download.
        save_path: The absolute path to save the image to. Must be an absolute path, otherwise the image will be saved relative to the server location.
    """
    urllib.request.urlretrieve(url, save_path)
    return {"success": True}

@mcp.tool()
async def run_workflow_from_file(file_path: str, return_prompt_id: bool = False) -> Any:
    """Run a workflow from a file.

    Args:
        file_path: The absolute path to the file to run.
        return_prompt_id: Whether to return the prompt ID along with images for status tracking.
    """
    with open(file_path, "r", encoding="utf-8") as f:
        workflow = json.load(f)

    auth = os.environ.get("COMFYUI_AUTHENTICATION")
    comfy = ComfyUI(
        url=f'http://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}',
        authentication=auth
    )
    result = await comfy.process_workflow(
        workflow,
        {},
        return_url=os.environ.get("RETURN_URL", "true").lower() == "true",
        return_prompt_id=return_prompt_id
    )
    return result

@mcp.tool()
async def run_workflow_from_json(json_data: dict, return_prompt_id: bool = False) -> Any:
    """Run a workflow from a JSON data.

    Args:
        json_data: The JSON data to run.
        return_prompt_id: Whether to return the prompt ID along with images for status tracking.
    """
    workflow = json_data

    auth = os.environ.get("COMFYUI_AUTHENTICATION")
    comfy = ComfyUI(
        url=f'http://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}',
        authentication=auth
    )
    result = await comfy.process_workflow(
        workflow,
        {},
        return_url=os.environ.get("RETURN_URL", "true").lower() == "true",
        return_prompt_id=return_prompt_id
    )
    return result

@mcp.tool()
async def get_task_status(prompt_id: str) -> Any:
    """Get the status of a task by its prompt ID.

    Args:
        prompt_id: The prompt ID of the task to check status for.
    """
    auth = os.environ.get("COMFYUI_AUTHENTICATION")
    comfy = ComfyUI(
        url=f'http://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}',
        authentication=auth
    )

    try:
        # Get task history to check if it's completed
        history = comfy.get_history(prompt_id)

        if prompt_id in history:
            task_data = history[prompt_id]
            status = {
                "prompt_id": prompt_id,
                "status": "completed",
                "outputs": task_data.get("outputs", {}),
                "status_str": task_data.get("status", {}).get("status_str", ""),
                "completed": task_data.get("status", {}).get("completed", False)
            }
            return status
        else:
            # Check if task is in queue
            queue_info = comfy.get_queue()

            # Check running tasks
            for task in queue_info.get("queue_running", []):
                if task[1] == prompt_id:
                    return {
                        "prompt_id": prompt_id,
                        "status": "running",
                        "position": 0
                    }

            # Check pending tasks
            for i, task in enumerate(queue_info.get("queue_pending", [])):
                if task[1] == prompt_id:
                    return {
                        "prompt_id": prompt_id,
                        "status": "pending",
                        "position": i + 1
                    }

            return {
                "prompt_id": prompt_id,
                "status": "not_found",
                "message": "Task not found in queue or history"
            }
    except Exception as e:
        return {
            "prompt_id": prompt_id,
            "status": "error",
            "error": str(e)
        }

@mcp.tool()
async def download_task_files(prompt_id: str, save_directory: str) -> Any:
    """Download all generated files from a completed task.

    Args:
        prompt_id: The prompt ID of the completed task.
        save_directory: The directory to save the downloaded files to.
    """
    auth = os.environ.get("COMFYUI_AUTHENTICATION")
    comfy = ComfyUI(
        url=f'http://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}',
        authentication=auth
    )

    try:
        # Get task history
        history = comfy.get_history(prompt_id)

        if prompt_id not in history:
            return {
                "success": False,
                "error": "Task not found or not completed"
            }

        task_data = history[prompt_id]
        outputs = task_data.get("outputs", {})

        # Create save directory if it doesn't exist
        os.makedirs(save_directory, exist_ok=True)

        downloaded_files = []

        # Download all images from all output nodes
        for node_id, node_output in outputs.items():
            if "images" in node_output:
                for i, image_info in enumerate(node_output["images"]):
                    # Get image data
                    image_data = comfy.get_image(
                        image_info["filename"],
                        image_info["subfolder"],
                        image_info["type"]
                    )

                    # Generate filename
                    original_filename = image_info["filename"]
                    filename = f"{prompt_id}_{node_id}_{i}_{original_filename}"
                    file_path = os.path.join(save_directory, filename)

                    # Save file
                    with open(file_path, "wb") as f:
                        f.write(image_data)

                    downloaded_files.append({
                        "node_id": node_id,
                        "original_filename": original_filename,
                        "saved_path": file_path,
                        "file_size": len(image_data)
                    })

        return {
            "success": True,
            "prompt_id": prompt_id,
            "downloaded_files": downloaded_files,
            "total_files": len(downloaded_files)
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    mcp.run(transport=os.environ.get("MCP_TRANSPORT", "stdio"))
