#!/usr/bin/env python3
"""
Test script for the new task status and file download features.
"""

import asyncio
import os
import tempfile
from src.client.comfyui import ComfyUI
from src.server import get_task_status, download_task_files, text_to_image

async def test_task_status_and_download():
    """Test the new task status and download functionality."""
    
    print("Testing new ComfyUI MCP server features...")
    
    # Test 1: Generate an image with prompt_id return
    print("\n1. Testing text_to_image with prompt_id return...")
    try:
        result = await text_to_image(
            prompt="a beautiful sunset over mountains",
            seed=42,
            steps=20,
            cfg=7.0,
            denoise=1.0,
            return_prompt_id=True
        )
        
        if isinstance(result, dict) and "prompt_id" in result:
            prompt_id = result["prompt_id"]
            print(f"✓ Generated image with prompt_id: {prompt_id}")
            
            # Test 2: Check task status
            print(f"\n2. Testing get_task_status for prompt_id: {prompt_id}")
            status = await get_task_status(prompt_id)
            print(f"✓ Task status: {status}")
            
            # Test 3: Download task files
            if status.get("status") == "completed":
                print(f"\n3. Testing download_task_files for prompt_id: {prompt_id}")
                
                # Create a temporary directory for downloads
                with tempfile.TemporaryDirectory() as temp_dir:
                    download_result = await download_task_files(prompt_id, temp_dir)
                    print(f"✓ Download result: {download_result}")
                    
                    if download_result.get("success"):
                        print(f"✓ Successfully downloaded {download_result.get('total_files', 0)} files")
                        for file_info in download_result.get("downloaded_files", []):
                            print(f"  - {file_info['original_filename']} -> {file_info['saved_path']}")
            else:
                print(f"⚠ Task not completed yet, status: {status.get('status')}")
                
        else:
            print("✗ Failed to get prompt_id from text_to_image result")
            
    except Exception as e:
        print(f"✗ Error during testing: {e}")
    
    # Test 4: Test status check for non-existent task
    print("\n4. Testing get_task_status for non-existent task...")
    try:
        fake_status = await get_task_status("non-existent-prompt-id")
        print(f"✓ Non-existent task status: {fake_status}")
    except Exception as e:
        print(f"✗ Error checking non-existent task: {e}")

def test_comfyui_client():
    """Test the ComfyUI client new methods."""
    print("\n5. Testing ComfyUI client new methods...")
    
    try:
        # Initialize client
        comfy = ComfyUI(
            url=f'http://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}',
            authentication=os.environ.get("COMFYUI_AUTHENTICATION")
        )
        
        # Test get_queue method
        queue_info = comfy.get_queue()
        print(f"✓ Queue info: {queue_info}")
        
    except Exception as e:
        print(f"✗ Error testing ComfyUI client: {e}")

if __name__ == "__main__":
    print("ComfyUI MCP Server - New Features Test")
    print("=" * 50)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Test ComfyUI client methods
    test_comfyui_client()
    
    # Test async functions
    asyncio.run(test_task_status_and_download())
    
    print("\n" + "=" * 50)
    print("Test completed!")
