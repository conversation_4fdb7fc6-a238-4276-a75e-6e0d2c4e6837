{"13": {"inputs": {"audio": "你能坚持这样挑战30天自律吗.WAV", "audioUI": ""}, "class_type": "LoadAudio", "_meta": {"title": "加载音频"}}, "28": {"inputs": {"version": "v1.5", "top_k": 30, "top_p": 0.8, "temperature": 1, "num_beams": 3, "max_mel_tokens": 1000, "max_text_tokens_per_sentence": 120, "sentences_bucket_max_size": 4, "fast_inference": true, "custom_cuda_kernel": false, "unload_model": true, "audio": ["13", 0], "text": ["40", 0]}, "class_type": "IndexTTSRun", "_meta": {"title": "IndexTTS Run"}}, "38": {"inputs": {"filename_prefix": "audio/indextts_", "audioUI": "", "audio": ["28", 0]}, "class_type": "SaveAudio", "_meta": {"title": "保存音频"}}, "40": {"inputs": {"value": "很多朋友问，为什么音驱数字人总是“开头还行，越说越崩”？\n答案很简单：传统做法用外部音频编码器挤特征，再用交叉注意力硬塞进扩散模型。扩散模型本身没有声音先验，长视频推理时误差会一段段地叠，嘴型飘，人设散。"}, "class_type": "PrimitiveStringMultiline", "_meta": {"title": "String (Multiline)"}}}